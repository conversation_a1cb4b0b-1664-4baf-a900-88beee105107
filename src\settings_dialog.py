import json
import os
from typing import Dict, Any, Optional
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QTabWidget,
    QWidget, QLabel, QPushButton, QSpinBox, QDoubleSpinBox,
    QCheckBox, QComboBox, QLineEdit, QGroupBox, QSlider,
    QMessageBox, QFileDialog, QTextEdit, QFrame, QButtonGroup,
    QRadioButton, QScrollArea
)
from PySide6.QtCore import Qt, Signal, QSettings
from PySide6.QtGui import QFont, QIntValidator, QDoubleValidator
from config import (
    WEBCAM_CONFIG, MEDIAPIPE_CONFIG, GESTURE_CONFIG, PERFORMANCE_CONFIG,
    UI_CONFIG, CUSTOM_GESTURE_CONFIG, ACTION_EXECUTION_CONFIG, PROJECT_ROOT
)


class SettingsDialog(QDialog):
    """
    GFLOW-17: Comprehensive Settings Dialog
    
    Provides user-friendly interface for modifying all application settings
    without requiring manual configuration file editing.
    """
    
    settings_changed = Signal(dict)  # Emitted when settings are applied
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("GestureFlow Settings")
        self.setModal(True)
        self.resize(800, 600)
        
        # Store original settings for cancel functionality
        self.original_settings = self.get_current_settings()
        self.current_settings = self.original_settings.copy()
        
        # QSettings for persistent storage
        self.qsettings = QSettings("GestureFlow", "Settings")
        
        self.setup_ui()
        self.setup_connections()
        self.load_settings()
    
    def setup_ui(self):
        """Setup the user interface with tabbed settings"""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Application Settings")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_webcam_tab()
        self.create_mediapipe_tab()
        self.create_gesture_tab()
        self.create_performance_tab()
        self.create_ui_tab()
        self.create_custom_gesture_tab()
        self.create_action_execution_tab()
        
        # Button layout
        button_layout = QHBoxLayout()
        
        # Reset to defaults button
        self.reset_button = QPushButton("Reset to Defaults")
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        # Standard buttons
        self.cancel_button = QPushButton("Cancel")
        self.apply_button = QPushButton("Apply")
        self.ok_button = QPushButton("OK")
        
        # Style standard buttons
        for btn in [self.cancel_button, self.apply_button, self.ok_button]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.apply_button)
        button_layout.addWidget(self.ok_button)
        
        layout.addLayout(button_layout)
    
    def create_webcam_tab(self):
        """Create webcam settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Webcam Configuration Group
        webcam_group = QGroupBox("Webcam Configuration")
        webcam_layout = QFormLayout(webcam_group)
        
        # Resolution settings
        self.width_spin = QSpinBox()
        self.width_spin.setRange(320, 1920)
        self.width_spin.setValue(WEBCAM_CONFIG['width'])
        webcam_layout.addRow("Width:", self.width_spin)
        
        self.height_spin = QSpinBox()
        self.height_spin.setRange(240, 1080)
        self.height_spin.setValue(WEBCAM_CONFIG['height'])
        webcam_layout.addRow("Height:", self.height_spin)
        
        # FPS setting
        self.fps_spin = QSpinBox()
        self.fps_spin.setRange(10, 60)
        self.fps_spin.setValue(WEBCAM_CONFIG['fps'])
        webcam_layout.addRow("FPS:", self.fps_spin)
        
        # Device ID
        self.device_spin = QSpinBox()
        self.device_spin.setRange(0, 10)
        self.device_spin.setValue(WEBCAM_CONFIG['device_id'])
        webcam_layout.addRow("Device ID:", self.device_spin)
        
        # Flip horizontal
        self.flip_check = QCheckBox()
        self.flip_check.setChecked(WEBCAM_CONFIG['flip_horizontal'])
        webcam_layout.addRow("Mirror Effect:", self.flip_check)
        
        layout.addWidget(webcam_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Webcam")
    
    def create_mediapipe_tab(self):
        """Create MediaPipe settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # MediaPipe Configuration Group
        mp_group = QGroupBox("MediaPipe Hand Tracking")
        mp_layout = QFormLayout(mp_group)
        
        # Max number of hands
        self.max_hands_spin = QSpinBox()
        self.max_hands_spin.setRange(1, 4)
        self.max_hands_spin.setValue(MEDIAPIPE_CONFIG['max_num_hands'])
        mp_layout.addRow("Max Hands:", self.max_hands_spin)
        
        # Detection confidence
        self.detection_conf_spin = QDoubleSpinBox()
        self.detection_conf_spin.setRange(0.1, 1.0)
        self.detection_conf_spin.setSingleStep(0.1)
        self.detection_conf_spin.setDecimals(2)
        self.detection_conf_spin.setValue(MEDIAPIPE_CONFIG['min_detection_confidence'])
        mp_layout.addRow("Detection Confidence:", self.detection_conf_spin)
        
        # Tracking confidence
        self.tracking_conf_spin = QDoubleSpinBox()
        self.tracking_conf_spin.setRange(0.1, 1.0)
        self.tracking_conf_spin.setSingleStep(0.1)
        self.tracking_conf_spin.setDecimals(2)
        self.tracking_conf_spin.setValue(MEDIAPIPE_CONFIG['min_tracking_confidence'])
        mp_layout.addRow("Tracking Confidence:", self.tracking_conf_spin)
        
        layout.addWidget(mp_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "MediaPipe")
    
    def create_gesture_tab(self):
        """Create gesture recognition settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Gesture Recognition Group
        gesture_group = QGroupBox("Gesture Recognition")
        gesture_layout = QFormLayout(gesture_group)
        
        # Recognition threshold
        self.recognition_threshold_spin = QDoubleSpinBox()
        self.recognition_threshold_spin.setRange(0.1, 1.0)
        self.recognition_threshold_spin.setSingleStep(0.1)
        self.recognition_threshold_spin.setDecimals(2)
        self.recognition_threshold_spin.setValue(GESTURE_CONFIG['recognition_threshold'])
        gesture_layout.addRow("Recognition Threshold:", self.recognition_threshold_spin)
        
        # Gesture hold time
        self.hold_time_spin = QDoubleSpinBox()
        self.hold_time_spin.setRange(0.1, 5.0)
        self.hold_time_spin.setSingleStep(0.1)
        self.hold_time_spin.setDecimals(1)
        self.hold_time_spin.setValue(GESTURE_CONFIG['gesture_hold_time'])
        gesture_layout.addRow("Hold Time (seconds):", self.hold_time_spin)
        
        # Smoothing frames
        self.smoothing_spin = QSpinBox()
        self.smoothing_spin.setRange(1, 10)
        self.smoothing_spin.setValue(GESTURE_CONFIG['smoothing_frames'])
        gesture_layout.addRow("Smoothing Frames:", self.smoothing_spin)
        
        # Debug mode
        self.debug_check = QCheckBox()
        self.debug_check.setChecked(GESTURE_CONFIG['debug_mode'])
        gesture_layout.addRow("Debug Mode:", self.debug_check)
        
        layout.addWidget(gesture_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Gesture Recognition")
    
    def create_performance_tab(self):
        """Create performance settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Performance Group
        perf_group = QGroupBox("Performance Settings")
        perf_layout = QFormLayout(perf_group)
        
        # FPS update interval
        self.fps_interval_spin = QSpinBox()
        self.fps_interval_spin.setRange(10, 100)
        self.fps_interval_spin.setValue(PERFORMANCE_CONFIG['fps_update_interval'])
        perf_layout.addRow("FPS Update Interval:", self.fps_interval_spin)
        
        # Recognition history
        self.history_spin = QSpinBox()
        self.history_spin.setRange(50, 500)
        self.history_spin.setValue(PERFORMANCE_CONFIG['max_recognition_history'])
        perf_layout.addRow("Max Recognition History:", self.history_spin)
        
        # Target FPS
        self.target_fps_spin = QSpinBox()
        self.target_fps_spin.setRange(10, 60)
        self.target_fps_spin.setValue(PERFORMANCE_CONFIG['target_fps'])
        perf_layout.addRow("Target FPS:", self.target_fps_spin)
        
        # Max latency
        self.max_latency_spin = QSpinBox()
        self.max_latency_spin.setRange(10, 200)
        self.max_latency_spin.setValue(PERFORMANCE_CONFIG['max_latency_ms'])
        perf_layout.addRow("Max Latency (ms):", self.max_latency_spin)
        
        layout.addWidget(perf_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Performance")

    def create_ui_tab(self):
        """Create UI settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # UI Configuration Group
        ui_group = QGroupBox("User Interface")
        ui_layout = QFormLayout(ui_group)

        # Window title
        self.window_title_edit = QLineEdit()
        self.window_title_edit.setText(UI_CONFIG['window_title'])
        ui_layout.addRow("Window Title:", self.window_title_edit)

        # Window dimensions
        self.window_width_spin = QSpinBox()
        self.window_width_spin.setRange(800, 2000)
        self.window_width_spin.setValue(UI_CONFIG['window_width'])
        ui_layout.addRow("Window Width:", self.window_width_spin)

        self.window_height_spin = QSpinBox()
        self.window_height_spin.setRange(600, 1500)
        self.window_height_spin.setValue(UI_CONFIG['window_height'])
        ui_layout.addRow("Window Height:", self.window_height_spin)

        # Video display dimensions
        self.video_width_spin = QSpinBox()
        self.video_width_spin.setRange(320, 1280)
        self.video_width_spin.setValue(UI_CONFIG['video_width'])
        ui_layout.addRow("Video Width:", self.video_width_spin)

        self.video_height_spin = QSpinBox()
        self.video_height_spin.setRange(240, 720)
        self.video_height_spin.setValue(UI_CONFIG['video_height'])
        ui_layout.addRow("Video Height:", self.video_height_spin)

        layout.addWidget(ui_group)
        layout.addStretch()

        self.tab_widget.addTab(tab, "User Interface")

    def create_custom_gesture_tab(self):
        """Create custom gesture settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Custom Gesture Configuration Group
        custom_group = QGroupBox("Custom Gesture Settings")
        custom_layout = QFormLayout(custom_group)

        # Samples per gesture
        self.samples_spin = QSpinBox()
        self.samples_spin.setRange(5, 50)
        self.samples_spin.setValue(CUSTOM_GESTURE_CONFIG['samples_per_gesture'])
        custom_layout.addRow("Samples per Gesture:", self.samples_spin)

        # Sample delay
        self.sample_delay_spin = QDoubleSpinBox()
        self.sample_delay_spin.setRange(0.5, 5.0)
        self.sample_delay_spin.setSingleStep(0.1)
        self.sample_delay_spin.setDecimals(1)
        self.sample_delay_spin.setValue(CUSTOM_GESTURE_CONFIG['sample_delay_seconds'])
        custom_layout.addRow("Sample Delay (seconds):", self.sample_delay_spin)

        # Recording countdown
        self.countdown_spin = QSpinBox()
        self.countdown_spin.setRange(1, 10)
        self.countdown_spin.setValue(CUSTOM_GESTURE_CONFIG['recording_countdown'])
        custom_layout.addRow("Recording Countdown:", self.countdown_spin)

        # Confidence threshold
        self.custom_confidence_spin = QDoubleSpinBox()
        self.custom_confidence_spin.setRange(0.1, 1.0)
        self.custom_confidence_spin.setSingleStep(0.1)
        self.custom_confidence_spin.setDecimals(2)
        self.custom_confidence_spin.setValue(CUSTOM_GESTURE_CONFIG['min_confidence_threshold'])
        custom_layout.addRow("Min Confidence:", self.custom_confidence_spin)

        # Similarity threshold
        self.similarity_spin = QDoubleSpinBox()
        self.similarity_spin.setRange(0.5, 1.0)
        self.similarity_spin.setSingleStep(0.05)
        self.similarity_spin.setDecimals(2)
        self.similarity_spin.setValue(CUSTOM_GESTURE_CONFIG['similarity_threshold'])
        custom_layout.addRow("Similarity Threshold:", self.similarity_spin)

        # Backup enabled
        self.backup_check = QCheckBox()
        self.backup_check.setChecked(CUSTOM_GESTURE_CONFIG['backup_enabled'])
        custom_layout.addRow("Enable Backup:", self.backup_check)

        # Gesture priority
        self.priority_check = QCheckBox()
        self.priority_check.setChecked(CUSTOM_GESTURE_CONFIG['enable_gesture_priority'])
        custom_layout.addRow("Predefined Priority:", self.priority_check)

        layout.addWidget(custom_group)
        layout.addStretch()

        self.tab_widget.addTab(tab, "Custom Gestures")

    def create_action_execution_tab(self):
        """Create action execution settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Action Execution Group
        action_group = QGroupBox("Action Execution Settings")
        action_layout = QFormLayout(action_group)

        # Input library selection
        self.input_library_combo = QComboBox()
        self.input_library_combo.addItems(['pynput', 'pyautogui'])
        self.input_library_combo.setCurrentText(ACTION_EXECUTION_CONFIG['input_library'])
        action_layout.addRow("Input Library:", self.input_library_combo)

        # Enable failsafe
        self.failsafe_check = QCheckBox()
        self.failsafe_check.setChecked(ACTION_EXECUTION_CONFIG['enable_failsafe'])
        action_layout.addRow("Enable Failsafe:", self.failsafe_check)

        # Default action delay
        self.action_delay_spin = QDoubleSpinBox()
        self.action_delay_spin.setRange(0.0, 2.0)
        self.action_delay_spin.setSingleStep(0.1)
        self.action_delay_spin.setDecimals(2)
        self.action_delay_spin.setValue(ACTION_EXECUTION_CONFIG['default_action_delay'])
        action_layout.addRow("Action Delay (seconds):", self.action_delay_spin)

        # Mouse movement duration
        self.mouse_duration_spin = QDoubleSpinBox()
        self.mouse_duration_spin.setRange(0.1, 2.0)
        self.mouse_duration_spin.setSingleStep(0.1)
        self.mouse_duration_spin.setDecimals(1)
        self.mouse_duration_spin.setValue(ACTION_EXECUTION_CONFIG['mouse_movement_duration'])
        action_layout.addRow("Mouse Duration (seconds):", self.mouse_duration_spin)

        # Action timeout
        self.timeout_spin = QDoubleSpinBox()
        self.timeout_spin.setRange(1.0, 30.0)
        self.timeout_spin.setSingleStep(1.0)
        self.timeout_spin.setDecimals(1)
        self.timeout_spin.setValue(ACTION_EXECUTION_CONFIG['action_timeout'])
        action_layout.addRow("Action Timeout (seconds):", self.timeout_spin)

        # Log all actions
        self.log_actions_check = QCheckBox()
        self.log_actions_check.setChecked(ACTION_EXECUTION_CONFIG['log_all_actions'])
        action_layout.addRow("Log All Actions:", self.log_actions_check)

        # Async execution
        self.async_check = QCheckBox()
        self.async_check.setChecked(ACTION_EXECUTION_CONFIG['async_execution'])
        action_layout.addRow("Async Execution:", self.async_check)

        layout.addWidget(action_group)
        layout.addStretch()

        self.tab_widget.addTab(tab, "Action Execution")

    def setup_connections(self):
        """Setup signal connections"""
        self.ok_button.clicked.connect(self.accept_settings)
        self.apply_button.clicked.connect(self.apply_settings)
        self.cancel_button.clicked.connect(self.reject)
        self.reset_button.clicked.connect(self.reset_to_defaults)

    def get_current_settings(self) -> Dict[str, Any]:
        """Get current settings from configuration"""
        return {
            'webcam': WEBCAM_CONFIG.copy(),
            'mediapipe': MEDIAPIPE_CONFIG.copy(),
            'gesture': GESTURE_CONFIG.copy(),
            'performance': PERFORMANCE_CONFIG.copy(),
            'ui': UI_CONFIG.copy(),
            'custom_gesture': CUSTOM_GESTURE_CONFIG.copy(),
            'action_execution': ACTION_EXECUTION_CONFIG.copy()
        }

    def load_settings(self):
        """Load settings from persistent storage"""
        # Load from QSettings if available
        if self.qsettings.contains("settings"):
            try:
                settings_str = self.qsettings.value("settings")
                if settings_str:
                    saved_settings = json.loads(settings_str)
                    self.apply_loaded_settings(saved_settings)
            except (json.JSONDecodeError, Exception) as e:
                print(f"Error loading saved settings: {e}")

    def apply_loaded_settings(self, settings: Dict[str, Any]):
        """Apply loaded settings to UI controls"""
        try:
            # Webcam settings
            if 'webcam' in settings:
                webcam = settings['webcam']
                self.width_spin.setValue(webcam.get('width', WEBCAM_CONFIG['width']))
                self.height_spin.setValue(webcam.get('height', WEBCAM_CONFIG['height']))
                self.fps_spin.setValue(webcam.get('fps', WEBCAM_CONFIG['fps']))
                self.device_spin.setValue(webcam.get('device_id', WEBCAM_CONFIG['device_id']))
                self.flip_check.setChecked(webcam.get('flip_horizontal', WEBCAM_CONFIG['flip_horizontal']))

            # MediaPipe settings
            if 'mediapipe' in settings:
                mp = settings['mediapipe']
                self.max_hands_spin.setValue(mp.get('max_num_hands', MEDIAPIPE_CONFIG['max_num_hands']))
                self.detection_conf_spin.setValue(mp.get('min_detection_confidence', MEDIAPIPE_CONFIG['min_detection_confidence']))
                self.tracking_conf_spin.setValue(mp.get('min_tracking_confidence', MEDIAPIPE_CONFIG['min_tracking_confidence']))

            # Gesture settings
            if 'gesture' in settings:
                gesture = settings['gesture']
                self.recognition_threshold_spin.setValue(gesture.get('recognition_threshold', GESTURE_CONFIG['recognition_threshold']))
                self.hold_time_spin.setValue(gesture.get('gesture_hold_time', GESTURE_CONFIG['gesture_hold_time']))
                self.smoothing_spin.setValue(gesture.get('smoothing_frames', GESTURE_CONFIG['smoothing_frames']))
                self.debug_check.setChecked(gesture.get('debug_mode', GESTURE_CONFIG['debug_mode']))

            # Performance settings
            if 'performance' in settings:
                perf = settings['performance']
                self.fps_interval_spin.setValue(perf.get('fps_update_interval', PERFORMANCE_CONFIG['fps_update_interval']))
                self.history_spin.setValue(perf.get('max_recognition_history', PERFORMANCE_CONFIG['max_recognition_history']))
                self.target_fps_spin.setValue(perf.get('target_fps', PERFORMANCE_CONFIG['target_fps']))
                self.max_latency_spin.setValue(perf.get('max_latency_ms', PERFORMANCE_CONFIG['max_latency_ms']))

            # UI settings
            if 'ui' in settings:
                ui = settings['ui']
                self.window_title_edit.setText(ui.get('window_title', UI_CONFIG['window_title']))
                self.window_width_spin.setValue(ui.get('window_width', UI_CONFIG['window_width']))
                self.window_height_spin.setValue(ui.get('window_height', UI_CONFIG['window_height']))
                self.video_width_spin.setValue(ui.get('video_width', UI_CONFIG['video_width']))
                self.video_height_spin.setValue(ui.get('video_height', UI_CONFIG['video_height']))

            # Custom gesture settings
            if 'custom_gesture' in settings:
                custom = settings['custom_gesture']
                self.samples_spin.setValue(custom.get('samples_per_gesture', CUSTOM_GESTURE_CONFIG['samples_per_gesture']))
                self.sample_delay_spin.setValue(custom.get('sample_delay_seconds', CUSTOM_GESTURE_CONFIG['sample_delay_seconds']))
                self.countdown_spin.setValue(custom.get('recording_countdown', CUSTOM_GESTURE_CONFIG['recording_countdown']))
                self.custom_confidence_spin.setValue(custom.get('min_confidence_threshold', CUSTOM_GESTURE_CONFIG['min_confidence_threshold']))
                self.similarity_spin.setValue(custom.get('similarity_threshold', CUSTOM_GESTURE_CONFIG['similarity_threshold']))
                self.backup_check.setChecked(custom.get('backup_enabled', CUSTOM_GESTURE_CONFIG['backup_enabled']))
                self.priority_check.setChecked(custom.get('enable_gesture_priority', CUSTOM_GESTURE_CONFIG['enable_gesture_priority']))

            # Action execution settings
            if 'action_execution' in settings:
                action = settings['action_execution']
                self.input_library_combo.setCurrentText(action.get('input_library', ACTION_EXECUTION_CONFIG['input_library']))
                self.failsafe_check.setChecked(action.get('enable_failsafe', ACTION_EXECUTION_CONFIG['enable_failsafe']))
                self.action_delay_spin.setValue(action.get('default_action_delay', ACTION_EXECUTION_CONFIG['default_action_delay']))
                self.mouse_duration_spin.setValue(action.get('mouse_movement_duration', ACTION_EXECUTION_CONFIG['mouse_movement_duration']))
                self.timeout_spin.setValue(action.get('action_timeout', ACTION_EXECUTION_CONFIG['action_timeout']))
                self.log_actions_check.setChecked(action.get('log_all_actions', ACTION_EXECUTION_CONFIG['log_all_actions']))
                self.async_check.setChecked(action.get('async_execution', ACTION_EXECUTION_CONFIG['async_execution']))

        except Exception as e:
            print(f"Error applying loaded settings: {e}")

    def collect_current_settings(self) -> Dict[str, Any]:
        """Collect current settings from UI controls"""
        return {
            'webcam': {
                'width': self.width_spin.value(),
                'height': self.height_spin.value(),
                'fps': self.fps_spin.value(),
                'device_id': self.device_spin.value(),
                'flip_horizontal': self.flip_check.isChecked()
            },
            'mediapipe': {
                'static_image_mode': MEDIAPIPE_CONFIG['static_image_mode'],  # Keep original
                'max_num_hands': self.max_hands_spin.value(),
                'min_detection_confidence': self.detection_conf_spin.value(),
                'min_tracking_confidence': self.tracking_conf_spin.value()
            },
            'gesture': {
                'recognition_threshold': self.recognition_threshold_spin.value(),
                'gesture_hold_time': self.hold_time_spin.value(),
                'smoothing_frames': self.smoothing_spin.value(),
                'debug_mode': self.debug_check.isChecked()
            },
            'performance': {
                'fps_update_interval': self.fps_interval_spin.value(),
                'max_recognition_history': self.history_spin.value(),
                'target_fps': self.target_fps_spin.value(),
                'max_latency_ms': self.max_latency_spin.value()
            },
            'ui': {
                'window_title': self.window_title_edit.text(),
                'window_width': self.window_width_spin.value(),
                'window_height': self.window_height_spin.value(),
                'video_width': self.video_width_spin.value(),
                'video_height': self.video_height_spin.value()
            },
            'custom_gesture': {
                'data_directory': CUSTOM_GESTURE_CONFIG['data_directory'],  # Keep original paths
                'models_directory': CUSTOM_GESTURE_CONFIG['models_directory'],
                'samples_per_gesture': self.samples_spin.value(),
                'sample_delay_seconds': self.sample_delay_spin.value(),
                'recording_countdown': self.countdown_spin.value(),
                'min_confidence_threshold': self.custom_confidence_spin.value(),
                'feature_vector_size': CUSTOM_GESTURE_CONFIG['feature_vector_size'],  # Keep original
                'similarity_threshold': self.similarity_spin.value(),
                'svm_kernel': CUSTOM_GESTURE_CONFIG['svm_kernel'],  # Keep original
                'svm_c': CUSTOM_GESTURE_CONFIG['svm_c'],  # Keep original
                'cross_validation_folds': CUSTOM_GESTURE_CONFIG['cross_validation_folds'],  # Keep original
                'max_gesture_name_length': CUSTOM_GESTURE_CONFIG['max_gesture_name_length'],  # Keep original
                'backup_enabled': self.backup_check.isChecked(),
                'predefined_confidence_boost': CUSTOM_GESTURE_CONFIG['predefined_confidence_boost'],  # Keep original
                'enable_gesture_priority': self.priority_check.isChecked()
            },
            'action_execution': {
                'input_library': self.input_library_combo.currentText(),
                'enable_failsafe': self.failsafe_check.isChecked(),
                'failsafe_corner': ACTION_EXECUTION_CONFIG['failsafe_corner'],  # Keep original
                'require_confirmation': ACTION_EXECUTION_CONFIG['require_confirmation'],  # Keep original
                'default_action_delay': self.action_delay_spin.value(),
                'mouse_movement_duration': self.mouse_duration_spin.value(),
                'key_press_interval': ACTION_EXECUTION_CONFIG['key_press_interval'],  # Keep original
                'action_timeout': self.timeout_spin.value(),
                'max_retry_attempts': ACTION_EXECUTION_CONFIG['max_retry_attempts'],  # Keep original
                'error_recovery_delay': ACTION_EXECUTION_CONFIG['error_recovery_delay'],  # Keep original
                'log_all_actions': self.log_actions_check.isChecked(),
                'enable_undo': ACTION_EXECUTION_CONFIG['enable_undo'],  # Keep original
                'async_execution': self.async_check.isChecked(),
                'queue_max_size': ACTION_EXECUTION_CONFIG['queue_max_size'],  # Keep original
                'execution_thread_pool': ACTION_EXECUTION_CONFIG['execution_thread_pool'],  # Keep original
                'context_aware_execution': ACTION_EXECUTION_CONFIG['context_aware_execution'],  # Keep original
                'application_detection': ACTION_EXECUTION_CONFIG['application_detection'],  # Keep original
                'window_focus_check': ACTION_EXECUTION_CONFIG['window_focus_check']  # Keep original
            }
        }

    def apply_settings(self):
        """Apply current settings"""
        try:
            # Collect current settings
            new_settings = self.collect_current_settings()

            # Validate settings
            if not self.validate_settings(new_settings):
                return

            # Save to persistent storage
            self.qsettings.setValue("settings", json.dumps(new_settings))

            # Update current settings
            self.current_settings = new_settings

            # Emit signal for main application to update
            self.settings_changed.emit(new_settings)

            QMessageBox.information(
                self, "Settings Applied",
                "Settings have been applied successfully.\nSome changes may require restarting the application."
            )

        except Exception as e:
            QMessageBox.critical(
                self, "Error",
                f"Failed to apply settings: {str(e)}"
            )

    def accept_settings(self):
        """Apply settings and close dialog"""
        self.apply_settings()
        self.accept()

    def validate_settings(self, settings: Dict[str, Any]) -> bool:
        """Validate settings before applying"""
        try:
            # Validate webcam settings
            webcam = settings['webcam']
            if webcam['width'] < 320 or webcam['height'] < 240:
                QMessageBox.warning(self, "Invalid Settings", "Webcam resolution too low.")
                return False

            # Validate MediaPipe settings
            mp = settings['mediapipe']
            if mp['min_detection_confidence'] > mp['min_tracking_confidence']:
                QMessageBox.warning(
                    self, "Invalid Settings",
                    "Detection confidence should not be higher than tracking confidence."
                )
                return False

            # Validate performance settings
            perf = settings['performance']
            if perf['target_fps'] > webcam['fps']:
                QMessageBox.warning(
                    self, "Invalid Settings",
                    "Target FPS cannot be higher than webcam FPS."
                )
                return False

            return True

        except Exception as e:
            QMessageBox.critical(self, "Validation Error", f"Settings validation failed: {str(e)}")
            return False

    def reset_to_defaults(self):
        """Reset all settings to default values"""
        reply = QMessageBox.question(
            self, "Reset Settings",
            "Are you sure you want to reset all settings to default values?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Reset to original configuration values
            self.apply_loaded_settings(self.original_settings)

            # Clear persistent storage
            self.qsettings.remove("settings")

            QMessageBox.information(
                self, "Settings Reset",
                "All settings have been reset to default values."
            )

    def save_settings_to_file(self):
        """Save current settings to a file"""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self, "Save Settings",
                os.path.join(PROJECT_ROOT, "settings_backup.json"),
                "JSON Files (*.json)"
            )

            if filename:
                settings = self.collect_current_settings()
                with open(filename, 'w') as f:
                    json.dump(settings, f, indent=2)

                QMessageBox.information(
                    self, "Settings Saved",
                    f"Settings saved to {filename}"
                )

        except Exception as e:
            QMessageBox.critical(
                self, "Error",
                f"Failed to save settings: {str(e)}"
            )

    def load_settings_from_file(self):
        """Load settings from a file"""
        try:
            filename, _ = QFileDialog.getOpenFileName(
                self, "Load Settings",
                PROJECT_ROOT,
                "JSON Files (*.json)"
            )

            if filename:
                with open(filename, 'r') as f:
                    settings = json.load(f)

                self.apply_loaded_settings(settings)

                QMessageBox.information(
                    self, "Settings Loaded",
                    f"Settings loaded from {filename}"
                )

        except Exception as e:
            QMessageBox.critical(
                self, "Error",
                f"Failed to load settings: {str(e)}"
            )
