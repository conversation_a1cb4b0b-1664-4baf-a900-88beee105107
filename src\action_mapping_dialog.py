import os
import uuid
from typing import Dict, List, Optional, Any
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QComboBox, QPushButton, QLineEdit, QSpinBox, QDoubleSpinBox,
    QTextEdit, QCheckBox, QGroupBox, QTabWidget, QWidget, QTableWidget,
    QTableWidgetItem, QHeaderView, QMessageBox, QFileDialog, QProgressBar,
    QSplitter, QFrame, QScrollArea, QButtonGroup, QRadioButton
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QFont, QIcon, QPalette, QColor
from action_types import (
    Action, ActionType, MouseAction, KeyboardAction, ApplicationAction,
    MouseActionParameters, KeyboardActionParameters, ApplicationActionParameters,
    MacroActionParameters, ActionValidator
)
from action_mapping_manager import ActionMappingManager
from action_executor import ActionExecutor
from config import ACTION_TYPES_CONFIG, PREDEFINED_GESTURES

class ActionPreviewWorker(QThread):
    """Worker thread for action preview/testing"""
    preview_completed = Signal(bool, str)
    
    def __init__(self, action: Action, executor: ActionExecutor):
        super().__init__()
        self.action = action
        self.executor = executor
    
    def run(self):
        try:
            future = self.executor.execute_action(self.action, async_execution=False)
            result = future.result()
            self.preview_completed.emit(result.success, result.message)
        except Exception as e:
            self.preview_completed.emit(False, f"Preview failed: {str(e)}")


class ActionMappingDialog(QDialog):
    """
    Dialog for creating and managing gesture-to-action mappings
    
    Features:
    - Intuitive gesture selection
    - Comprehensive action configuration
    - Action preview/testing
    - Mapping management
    - Profile support
    """
    
    mapping_created = Signal(str)  # Emitted when a new mapping is created
    mapping_updated = Signal(str)  # Emitted when a mapping is updated
    
    def __init__(self, mapping_manager: ActionMappingManager, 
                 custom_gesture_manager, parent=None):
        super().__init__(parent)
        self.mapping_manager = mapping_manager
        self.custom_gesture_manager = custom_gesture_manager
        self.action_executor = ActionExecutor()
        self.validator = ActionValidator()
        
        # Current state
        self.current_mapping_id = None
        self.preview_worker = None
        
        self.setWindowTitle("Gesture-to-Action Mapping")
        self.setModal(True)
        self.resize(900, 700)
        
        self.setup_ui()
        self.setup_connections()
        self.load_available_gestures()
        self.load_existing_mappings()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Create main splitter
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # Left panel - Mapping list and controls
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Action configuration
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([300, 600])
        
        # Bottom buttons
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("Save Mapping")
        self.save_button.setEnabled(False)
        
        self.test_button = QPushButton("Test Action")
        self.test_button.setEnabled(False)
        
        self.delete_button = QPushButton("Delete Mapping")
        self.delete_button.setEnabled(False)
        
        self.close_button = QPushButton("Close")
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.test_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
    
    def create_left_panel(self) -> QWidget:
        """Create the left panel with mapping list and gesture selection"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Profile selection
        profile_group = QGroupBox("Profile")
        profile_layout = QVBoxLayout(profile_group)
        
        self.profile_combo = QComboBox()
        self.load_profiles()
        profile_layout.addWidget(self.profile_combo)
        
        layout.addWidget(profile_group)
        
        # Gesture selection
        gesture_group = QGroupBox("Select Gesture")
        gesture_layout = QFormLayout(gesture_group)
        
        self.gesture_type_combo = QComboBox()
        self.gesture_type_combo.addItems(["Predefined", "Custom"])
        
        self.gesture_combo = QComboBox()
        
        gesture_layout.addRow("Gesture Type:", self.gesture_type_combo)
        gesture_layout.addRow("Gesture:", self.gesture_combo)
        
        layout.addWidget(gesture_group)
        
        # Existing mappings
        mappings_group = QGroupBox("Existing Mappings")
        mappings_layout = QVBoxLayout(mappings_group)
        
        self.mappings_table = QTableWidget()
        self.mappings_table.setColumnCount(4)
        self.mappings_table.setHorizontalHeaderLabels([
            "Gesture", "Action Type", "Enabled", "Uses"
        ])
        self.mappings_table.horizontalHeader().setStretchLastSection(True)
        self.mappings_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        mappings_layout.addWidget(self.mappings_table)
        
        layout.addWidget(mappings_group)
        
        return widget
    
    def create_right_panel(self) -> QWidget:
        """Create the right panel with action configuration"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Action type selection
        action_type_group = QGroupBox("Action Type")
        action_type_layout = QHBoxLayout(action_type_group)
        
        self.action_type_group = QButtonGroup()
        
        for action_type in ActionType:
            if ACTION_TYPES_CONFIG.get(action_type.value, {}).get('enabled', False):
                radio = QRadioButton(action_type.value.title())
                radio.setProperty('action_type', action_type.value)
                self.action_type_group.addButton(radio)
                action_type_layout.addWidget(radio)
        
        layout.addWidget(action_type_group)
        
        # Action configuration tabs
        self.action_tabs = QTabWidget()
        
        # Mouse actions tab
        self.mouse_tab = self.create_mouse_tab()
        self.action_tabs.addTab(self.mouse_tab, "Mouse")
        
        # Keyboard actions tab
        self.keyboard_tab = self.create_keyboard_tab()
        self.action_tabs.addTab(self.keyboard_tab, "Keyboard")
        
        # Application actions tab
        self.application_tab = self.create_application_tab()
        self.action_tabs.addTab(self.application_tab, "Application")
        
        # Macro actions tab
        self.macro_tab = self.create_macro_tab()
        self.action_tabs.addTab(self.macro_tab, "Macro")
        
        layout.addWidget(self.action_tabs)
        
        # Action settings
        settings_group = QGroupBox("Action Settings")
        settings_layout = QFormLayout(settings_group)
        
        self.action_name_edit = QLineEdit()
        self.action_description_edit = QTextEdit()
        self.action_description_edit.setMaximumHeight(60)
        
        self.enabled_checkbox = QCheckBox()
        self.enabled_checkbox.setChecked(True)
        
        self.confirmation_checkbox = QCheckBox()
        
        self.timeout_spinbox = QDoubleSpinBox()
        self.timeout_spinbox.setRange(0.1, 60.0)
        self.timeout_spinbox.setValue(5.0)
        self.timeout_spinbox.setSuffix(" seconds")
        
        settings_layout.addRow("Name:", self.action_name_edit)
        settings_layout.addRow("Description:", self.action_description_edit)
        settings_layout.addRow("Enabled:", self.enabled_checkbox)
        settings_layout.addRow("Require Confirmation:", self.confirmation_checkbox)
        settings_layout.addRow("Timeout:", self.timeout_spinbox)
        
        layout.addWidget(settings_group)
        
        # Preview area
        preview_group = QGroupBox("Action Preview")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(80)
        self.preview_text.setReadOnly(True)
        
        self.preview_progress = QProgressBar()
        self.preview_progress.setVisible(False)
        
        preview_layout.addWidget(self.preview_text)
        preview_layout.addWidget(self.preview_progress)
        
        layout.addWidget(preview_group)
        
        return widget
    
    def create_mouse_tab(self) -> QWidget:
        """Create mouse action configuration tab"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        self.mouse_action_combo = QComboBox()
        mouse_actions = ACTION_TYPES_CONFIG.get('mouse', {}).get('actions', [])
        self.mouse_action_combo.addItems([action.replace('_', ' ').title() for action in mouse_actions])
        
        self.mouse_x_spinbox = QSpinBox()
        self.mouse_x_spinbox.setRange(0, 9999)
        self.mouse_x_spinbox.setSpecialValueText("Current")
        
        self.mouse_y_spinbox = QSpinBox()
        self.mouse_y_spinbox.setRange(0, 9999)
        self.mouse_y_spinbox.setSpecialValueText("Current")
        
        self.mouse_button_combo = QComboBox()
        self.mouse_button_combo.addItems(["Left", "Right", "Middle"])
        
        self.mouse_clicks_spinbox = QSpinBox()
        self.mouse_clicks_spinbox.setRange(1, 10)
        self.mouse_clicks_spinbox.setValue(1)
        
        self.mouse_duration_spinbox = QDoubleSpinBox()
        self.mouse_duration_spinbox.setRange(0.0, 10.0)
        self.mouse_duration_spinbox.setValue(0.3)
        self.mouse_duration_spinbox.setSuffix(" seconds")
        
        self.scroll_direction_combo = QComboBox()
        self.scroll_direction_combo.addItems(["Up", "Down", "Left", "Right"])
        
        self.scroll_amount_spinbox = QSpinBox()
        self.scroll_amount_spinbox.setRange(1, 20)
        self.scroll_amount_spinbox.setValue(3)
        
        layout.addRow("Action:", self.mouse_action_combo)
        layout.addRow("X Position:", self.mouse_x_spinbox)
        layout.addRow("Y Position:", self.mouse_y_spinbox)
        layout.addRow("Button:", self.mouse_button_combo)
        layout.addRow("Click Count:", self.mouse_clicks_spinbox)
        layout.addRow("Duration:", self.mouse_duration_spinbox)
        layout.addRow("Scroll Direction:", self.scroll_direction_combo)
        layout.addRow("Scroll Amount:", self.scroll_amount_spinbox)
        
        return widget
    
    def create_keyboard_tab(self) -> QWidget:
        """Create keyboard action configuration tab"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        self.keyboard_action_combo = QComboBox()
        keyboard_actions = ACTION_TYPES_CONFIG.get('keyboard', {}).get('actions', [])
        self.keyboard_action_combo.addItems([action.replace('_', ' ').title() for action in keyboard_actions])
        
        self.keys_edit = QLineEdit()
        self.keys_edit.setPlaceholderText("e.g., ctrl+c, enter, f1")
        
        self.text_edit = QTextEdit()
        self.text_edit.setMaximumHeight(80)
        self.text_edit.setPlaceholderText("Text to type...")
        
        self.key_interval_spinbox = QDoubleSpinBox()
        self.key_interval_spinbox.setRange(0.0, 1.0)
        self.key_interval_spinbox.setValue(0.05)
        self.key_interval_spinbox.setSuffix(" seconds")
        
        layout.addRow("Action:", self.keyboard_action_combo)
        layout.addRow("Keys:", self.keys_edit)
        layout.addRow("Text:", self.text_edit)
        layout.addRow("Key Interval:", self.key_interval_spinbox)
        
        return widget
    
    def create_application_tab(self) -> QWidget:
        """Create application action configuration tab"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        self.app_action_combo = QComboBox()
        app_actions = ACTION_TYPES_CONFIG.get('application', {}).get('actions', [])
        self.app_action_combo.addItems([action.replace('_', ' ').title() for action in app_actions])
        
        self.app_path_edit = QLineEdit()
        
        self.app_browse_button = QPushButton("Browse...")
        
        path_layout = QHBoxLayout()
        path_layout.addWidget(self.app_path_edit)
        path_layout.addWidget(self.app_browse_button)
        
        self.app_args_edit = QLineEdit()
        self.app_args_edit.setPlaceholderText("Command line arguments (optional)")
        
        self.app_workdir_edit = QLineEdit()
        self.app_workdir_edit.setPlaceholderText("Working directory (optional)")
        
        layout.addRow("Action:", self.app_action_combo)
        layout.addRow("Application Path:", path_layout)
        layout.addRow("Arguments:", self.app_args_edit)
        layout.addRow("Working Directory:", self.app_workdir_edit)
        
        return widget
    
    def create_macro_tab(self) -> QWidget:
        """Create macro action configuration tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Macro settings
        settings_layout = QFormLayout()
        
        self.macro_loop_spinbox = QSpinBox()
        self.macro_loop_spinbox.setRange(1, 100)
        self.macro_loop_spinbox.setValue(1)
        
        self.macro_delay_spinbox = QDoubleSpinBox()
        self.macro_delay_spinbox.setRange(0.0, 10.0)
        self.macro_delay_spinbox.setValue(0.1)
        self.macro_delay_spinbox.setSuffix(" seconds")
        
        settings_layout.addRow("Loop Count:", self.macro_loop_spinbox)
        settings_layout.addRow("Delay Between Actions:", self.macro_delay_spinbox)
        
        layout.addLayout(settings_layout)
        
        # Macro sequence
        sequence_group = QGroupBox("Action Sequence")
        sequence_layout = QVBoxLayout(sequence_group)
        
        self.macro_table = QTableWidget()
        self.macro_table.setColumnCount(3)
        self.macro_table.setHorizontalHeaderLabels(["Order", "Action Type", "Description"])
        self.macro_table.horizontalHeader().setStretchLastSection(True)
        
        sequence_layout.addWidget(self.macro_table)
        
        # Macro buttons
        macro_buttons_layout = QHBoxLayout()
        
        self.add_action_button = QPushButton("Add Action")
        self.edit_action_button = QPushButton("Edit Action")
        self.remove_action_button = QPushButton("Remove Action")
        self.move_up_button = QPushButton("Move Up")
        self.move_down_button = QPushButton("Move Down")
        
        macro_buttons_layout.addWidget(self.add_action_button)
        macro_buttons_layout.addWidget(self.edit_action_button)
        macro_buttons_layout.addWidget(self.remove_action_button)
        macro_buttons_layout.addStretch()
        macro_buttons_layout.addWidget(self.move_up_button)
        macro_buttons_layout.addWidget(self.move_down_button)
        
        sequence_layout.addLayout(macro_buttons_layout)
        layout.addWidget(sequence_group)
        
        return widget

    def setup_connections(self):
        """Setup signal connections"""
        # Profile and gesture selection
        self.profile_combo.currentTextChanged.connect(self.on_profile_changed)
        self.gesture_type_combo.currentTextChanged.connect(self.load_available_gestures)
        self.gesture_combo.currentTextChanged.connect(self.on_gesture_selected)

        # Action type selection
        self.action_type_group.buttonClicked.connect(self.on_action_type_changed)

        # Application browse button
        self.app_browse_button.clicked.connect(self.browse_application)

        # Mapping table selection
        self.mappings_table.itemSelectionChanged.connect(self.on_mapping_selected)

        # Main buttons
        self.save_button.clicked.connect(self.save_mapping)
        self.test_button.clicked.connect(self.test_action)
        self.delete_button.clicked.connect(self.delete_mapping)
        self.close_button.clicked.connect(self.close)

    def load_profiles(self):
        """Load available profiles"""
        self.profile_combo.clear()
        profiles = self.mapping_manager.get_available_profiles()

        for profile in profiles:
            self.profile_combo.addItem(profile['name'])

        # Select current profile
        current_profile = self.mapping_manager.get_current_profile_name()
        index = self.profile_combo.findText(current_profile)
        if index >= 0:
            self.profile_combo.setCurrentIndex(index)

    def load_available_gestures(self):
        """Load available gestures based on selected type"""
        self.gesture_combo.clear()

        gesture_type = self.gesture_type_combo.currentText().lower()

        if gesture_type == "predefined":
            # Load predefined gestures
            for gesture_id, gesture_data in PREDEFINED_GESTURES.items():
                if gesture_data.get('enabled', True):
                    self.gesture_combo.addItem(gesture_data['name'], gesture_id)

        elif gesture_type == "custom":
            # Load custom gestures
            custom_gestures = self.custom_gesture_manager.get_gesture_list()
            for gesture_data in custom_gestures:
                if gesture_data.get('is_trained', False):
                    gesture_name = gesture_data['name']
                    self.gesture_combo.addItem(gesture_name, gesture_name)

    def load_existing_mappings(self):
        """Load existing mappings into the table"""
        self.mappings_table.setRowCount(0)

        mappings = self.mapping_manager.get_all_mappings(enabled_only=False)

        for mapping in mappings:
            row = self.mappings_table.rowCount()
            self.mappings_table.insertRow(row)

            # Gesture name
            gesture_item = QTableWidgetItem(mapping.gesture_name)
            gesture_item.setData(Qt.UserRole, mapping.id)
            self.mappings_table.setItem(row, 0, gesture_item)

            # Action type
            action_type_item = QTableWidgetItem(f"{mapping.action.type.value}.{mapping.action.subtype}")
            self.mappings_table.setItem(row, 1, action_type_item)

            # Enabled status
            enabled_item = QTableWidgetItem("Yes" if mapping.enabled else "No")
            self.mappings_table.setItem(row, 2, enabled_item)

            # Use count
            uses_item = QTableWidgetItem(str(mapping.use_count))
            self.mappings_table.setItem(row, 3, uses_item)

    def on_profile_changed(self, profile_name: str):
        """Handle profile change"""
        if profile_name and profile_name != self.mapping_manager.get_current_profile_name():
            self.mapping_manager.load_profile(profile_name)
            self.load_existing_mappings()
            self.clear_form()

    def on_gesture_selected(self):
        """Handle gesture selection"""
        self.update_form_state()

    def on_action_type_changed(self):
        """Handle action type change"""
        selected_button = self.action_type_group.checkedButton()
        if selected_button:
            action_type = selected_button.property('action_type')

            # Switch to appropriate tab
            tab_map = {
                'mouse': 0,
                'keyboard': 1,
                'application': 2,
                'macro': 3
            }

            if action_type in tab_map:
                self.action_tabs.setCurrentIndex(tab_map[action_type])

        self.update_form_state()

    def on_mapping_selected(self):
        """Handle mapping selection from table"""
        selected_items = self.mappings_table.selectedItems()
        if selected_items:
            mapping_id = selected_items[0].data(Qt.UserRole)
            self.load_mapping(mapping_id)
        else:
            self.clear_form()

    def load_mapping(self, mapping_id: str):
        """Load a mapping into the form"""
        mapping = self.mapping_manager.mappings.get(mapping_id)
        if not mapping:
            return

        self.current_mapping_id = mapping_id

        # Set gesture selection
        gesture_type = "Predefined" if mapping.gesture_type == "predefined" else "Custom"
        self.gesture_type_combo.setCurrentText(gesture_type)
        self.load_available_gestures()

        # Find and select the gesture
        for i in range(self.gesture_combo.count()):
            if self.gesture_combo.itemData(i) == mapping.gesture_name:
                self.gesture_combo.setCurrentIndex(i)
                break

        # Set action type
        action_type = mapping.action.type.value
        for button in self.action_type_group.buttons():
            if button.property('action_type') == action_type:
                button.setChecked(True)
                break

        self.on_action_type_changed()

        # Set action settings
        self.action_name_edit.setText(mapping.action.name)
        self.action_description_edit.setPlainText(mapping.action.description)
        self.enabled_checkbox.setChecked(mapping.enabled)
        self.confirmation_checkbox.setChecked(mapping.action.requires_confirmation)
        self.timeout_spinbox.setValue(mapping.action.timeout)

        self.update_form_state()

    def clear_form(self):
        """Clear the form"""
        self.current_mapping_id = None

        # Clear gesture selection
        self.gesture_combo.setCurrentIndex(-1)

        # Clear action type selection
        for button in self.action_type_group.buttons():
            button.setChecked(False)

        # Clear action settings
        self.action_name_edit.clear()
        self.action_description_edit.clear()
        self.enabled_checkbox.setChecked(True)
        self.confirmation_checkbox.setChecked(False)
        self.timeout_spinbox.setValue(5.0)

        self.update_form_state()

    def update_form_state(self):
        """Update form state based on current selections"""
        has_gesture = self.gesture_combo.currentIndex() >= 0
        has_action_type = self.action_type_group.checkedButton() is not None

        # Enable/disable save button
        self.save_button.setEnabled(has_gesture and has_action_type)

        # Enable/disable test button
        self.test_button.setEnabled(has_gesture and has_action_type)

        # Enable/disable delete button
        self.delete_button.setEnabled(self.current_mapping_id is not None)

    def browse_application(self):
        """Browse for application executable"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Application", "",
            "Executable Files (*.exe);;All Files (*.*)"
        )

        if file_path:
            self.app_path_edit.setText(file_path)

    def create_action_from_form(self) -> Optional[Action]:
        """Create an Action object from the current form data"""
        try:
            # Get selected action type
            selected_button = self.action_type_group.checkedButton()
            if not selected_button:
                return None

            action_type_str = selected_button.property('action_type')
            action_type = ActionType(action_type_str)

            # Create parameters based on action type
            if action_type == ActionType.MOUSE:
                subtype = self.mouse_action_combo.currentText().lower().replace(' ', '_')
                parameters = MouseActionParameters(
                    x=self.mouse_x_spinbox.value() if self.mouse_x_spinbox.value() > 0 else None,
                    y=self.mouse_y_spinbox.value() if self.mouse_y_spinbox.value() > 0 else None,
                    button=self.mouse_button_combo.currentText().lower(),
                    clicks=self.mouse_clicks_spinbox.value(),
                    duration=self.mouse_duration_spinbox.value(),
                    scroll_direction=self.scroll_direction_combo.currentText().lower(),
                    scroll_amount=self.scroll_amount_spinbox.value()
                )

            elif action_type == ActionType.KEYBOARD:
                subtype = self.keyboard_action_combo.currentText().lower().replace(' ', '_')
                keys_text = self.keys_edit.text().strip()

                # Parse keys
                if '+' in keys_text:
                    # Key combination
                    keys = [k.strip() for k in keys_text.split('+')]
                    modifiers = keys[:-1] if len(keys) > 1 else []
                    main_keys = [keys[-1]] if keys else []
                else:
                    # Single key or list
                    keys = [keys_text] if keys_text else []
                    modifiers = []
                    main_keys = keys

                parameters = KeyboardActionParameters(
                    keys=main_keys,
                    text=self.text_edit.toPlainText(),
                    modifiers=modifiers,
                    interval=self.key_interval_spinbox.value()
                )

            elif action_type == ActionType.APPLICATION:
                subtype = self.app_action_combo.currentText().lower().replace(' ', '_')
                args_text = self.app_args_edit.text().strip()
                arguments = args_text.split() if args_text else []

                parameters = ApplicationActionParameters(
                    path=self.app_path_edit.text().strip(),
                    arguments=arguments,
                    working_directory=self.app_workdir_edit.text().strip()
                )

            else:
                # For macro and other types, use basic parameters for now
                subtype = "execute"
                parameters = MacroActionParameters()

            # Create action
            action = Action(
                id=str(uuid.uuid4()),
                type=action_type,
                subtype=subtype,
                parameters=parameters,
                name=self.action_name_edit.text().strip(),
                description=self.action_description_edit.toPlainText().strip(),
                enabled=self.enabled_checkbox.isChecked(),
                requires_confirmation=self.confirmation_checkbox.isChecked(),
                timeout=self.timeout_spinbox.value()
            )

            return action

        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to create action: {str(e)}")
            return None

    def save_mapping(self):
        """Save the current mapping"""
        try:
            # Get gesture information
            gesture_data = self.gesture_combo.currentData()
            gesture_display_name = self.gesture_combo.currentText()
            gesture_type = "predefined" if self.gesture_type_combo.currentText() == "Predefined" else "custom"

            if not gesture_data or not gesture_display_name:
                QMessageBox.warning(self, "Error", "Please select a gesture.")
                return

            # Use gesture_data (ID) for predefined gestures, display name for custom gestures
            if gesture_type == "predefined":
                gesture_name = gesture_data  # Use the gesture ID (e.g., "open_palm")
            else:
                gesture_name = gesture_display_name  # Use the display name for custom gestures

            # Create action
            action = self.create_action_from_form()
            if not action:
                QMessageBox.warning(self, "Error", "Please configure the action.")
                return

            # Validate action
            is_valid, error_message = self.validator.validate_action(action)
            if not is_valid:
                QMessageBox.warning(self, "Validation Error", f"Action validation failed:\n{error_message}")
                return

            # Check for confirmation if required
            if self.validator.requires_confirmation(action):
                reply = QMessageBox.question(
                    self, "Confirm Action",
                    f"This action requires confirmation due to security settings.\n\n"
                    f"Action: {action.type.value}.{action.subtype}\n"
                    f"Do you want to proceed?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply != QMessageBox.Yes:
                    return

            # Save mapping
            if self.current_mapping_id:
                # Update existing mapping
                success = self.mapping_manager.update_mapping(
                    self.current_mapping_id,
                    action=action,
                    enabled=self.enabled_checkbox.isChecked()
                )

                if success:
                    QMessageBox.information(self, "Success", "Mapping updated successfully!")
                    self.mapping_updated.emit(self.current_mapping_id)
                else:
                    QMessageBox.warning(self, "Error", "Failed to update mapping.")
            else:
                # Check if mapping already exists
                existing_mapping = self.mapping_manager.get_mapping_for_gesture(gesture_name, gesture_type)
                if existing_mapping:
                    reply = QMessageBox.question(
                        self, "Mapping Already Exists",
                        f"A mapping already exists for gesture '{gesture_name}'.\n\n"
                        f"Current action: {existing_mapping.action.name}\n"
                        f"Action type: {existing_mapping.action.type.value}.{existing_mapping.action.subtype}\n\n"
                        f"Would you like to:\n"
                        f"• Yes: Update the existing mapping\n"
                        f"• No: Cancel and manually delete the existing mapping first",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )

                    if reply == QMessageBox.Yes:
                        # Update existing mapping
                        success = self.mapping_manager.update_mapping(
                            existing_mapping.id,
                            action=action,
                            enabled=self.enabled_checkbox.isChecked()
                        )

                        if success:
                            QMessageBox.information(self, "Success", "Mapping updated successfully!")
                            self.mapping_updated.emit(existing_mapping.id)
                            self.current_mapping_id = existing_mapping.id
                        else:
                            QMessageBox.warning(self, "Error", "Failed to update mapping.")
                    return

                # Create new mapping
                mapping_id = self.mapping_manager.add_mapping(gesture_name, gesture_type, action)

                if mapping_id:
                    QMessageBox.information(self, "Success", "Mapping created successfully!")
                    self.mapping_created.emit(mapping_id)
                    self.current_mapping_id = mapping_id
                else:
                    QMessageBox.warning(self, "Error", "Failed to create mapping. Please check if a mapping already exists for this gesture.")

            # Refresh the mappings table
            self.load_existing_mappings()
            self.update_form_state()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"An error occurred while saving:\n{str(e)}")

    def test_action(self):
        """Test the current action"""
        try:
            action = self.create_action_from_form()
            if not action:
                QMessageBox.warning(self, "Error", "Please configure the action first.")
                return

            # Validate action
            is_valid, error_message = self.validator.validate_action(action)
            if not is_valid:
                QMessageBox.warning(self, "Validation Error", f"Action validation failed:\n{error_message}")
                return

            # Confirm test execution
            reply = QMessageBox.question(
                self, "Test Action",
                f"This will execute the action for testing purposes.\n\n"
                f"Action: {action.type.value}.{action.subtype}\n"
                f"Are you sure you want to proceed?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # Show progress
            self.preview_progress.setVisible(True)
            self.preview_progress.setRange(0, 0)  # Indeterminate progress
            self.test_button.setEnabled(False)

            # Execute action in worker thread
            self.preview_worker = ActionPreviewWorker(action, self.action_executor)
            self.preview_worker.preview_completed.connect(self.on_test_completed)
            self.preview_worker.start()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"An error occurred during testing:\n{str(e)}")

    def on_test_completed(self, success: bool, message: str):
        """Handle test completion"""
        self.preview_progress.setVisible(False)
        self.test_button.setEnabled(True)

        if success:
            QMessageBox.information(self, "Test Result", f"Action executed successfully!\n\n{message}")
        else:
            QMessageBox.warning(self, "Test Result", f"Action execution failed:\n\n{message}")

    def delete_mapping(self):
        """Delete the current mapping"""
        if not self.current_mapping_id:
            return

        mapping = self.mapping_manager.mappings.get(self.current_mapping_id)
        if not mapping:
            return

        reply = QMessageBox.question(
            self, "Delete Mapping",
            f"Are you sure you want to delete the mapping for gesture '{mapping.gesture_name}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            success = self.mapping_manager.remove_mapping(self.current_mapping_id)

            if success:
                QMessageBox.information(self, "Success", "Mapping deleted successfully!")
                self.load_existing_mappings()
                self.clear_form()
            else:
                QMessageBox.warning(self, "Error", "Failed to delete mapping.")

    def closeEvent(self, event):
        """Handle dialog close event"""
        # Shutdown action executor
        if hasattr(self, 'action_executor'):
            self.action_executor.shutdown()

        # Stop any running preview worker
        if self.preview_worker and self.preview_worker.isRunning():
            self.preview_worker.terminate()
            self.preview_worker.wait()

        event.accept()
